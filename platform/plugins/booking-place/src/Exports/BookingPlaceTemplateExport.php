<?php

namespace Botble\BookingPlace\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class BookingPlaceTemplateExport implements FromArray, WithHeadings
{
    protected array $data;

    public function __construct(array $data = [])
    {
        if (empty($data)) {
            $this->data = $this->getDefaultTemplateData();
        } else {
            $this->data = $data;
        }
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'name',
            'slug',
            'description',
            'status',
            'thumbnail',
            'reservation_start_time',
            'reservation_end_time',
            'address',
            'address_2',
            'city',
            'state',
            'postcode',
            'country',
            'coordinates',
            'phone_numbers',
            'emails',
            'website',
            'social_links',
            'iframe',
            'categories',
            'services'
        ];
    }

    protected function getDefaultTemplateData(): array
    {
        return [
            [
                'name' => 'Sample Restaurant & Bar',
                'slug' => 'sample-restaurant-bar',
                'description' => 'A premium restaurant and bar with modern Vietnamese cuisine and craft cocktails. Located in the heart of Hanoi with beautiful city views.',
                'status' => 'published',
                'thumbnail' => '["https://letsenhance.io/static/8f5e523ee6b2479e26ecc91b9c25261e/396e9/MainAfter.jpg", "https://letsenhance.io/static/8f5e523ee6b2479e26ecc91b9c25261e/396e9/MainAfter.jpg"]',
                'reservation_start_time' => '11:00:00',
                'reservation_end_time' => '23:00:00',
                'address' => '123 Hoàn Kiếm Street',
                'address_2' => 'District 1, Floor 5',
                'city' => 'Hà Nội',
                'state' => 'Hà Nội',
                'postcode' => '10000',
                'country' => 'Vietnam',
                'coordinates' => '21.0285,-105.8542',
                'phone_numbers' => '["+84-24-1234-5678", "+84-90-123-4567"]',
                'emails' => '["<EMAIL>", "<EMAIL>"]',
                'website' => 'https://restaurant.com',
                'social_links' => '{"facebook": "https://facebook.com/restaurant", "instagram": "https://instagram.com/restaurant"}',
                'iframe' => '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.4609!2d105.8542!3d21.0285!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1" width="600" height="450" style="border:0;" allowfullscreen loading="lazy"></iframe>',
                'categories' => 'Restaurant, Bar',
                'services' => 'Free WiFi, Air Conditioning, Parking'
            ],
            [
                'name' => 'Luxury Sky Lounge',
                'slug' => 'luxury-sky-lounge',
                'description' => 'Exclusive rooftop lounge with panoramic city views, premium cocktails, and fine dining experience.',
                'status' => 'published',
                'thumbnail' => '["https://letsenhance.io/static/8f5e523ee6b2479e26ecc91b9c25261e/396e9/MainAfter.jpg"]',
                'reservation_start_time' => '17:00:00',
                'reservation_end_time' => '02:00:00',
                'address' => '456 Tây Hồ Avenue',
                'address_2' => 'Rooftop Level',
                'city' => 'Hà Nội',
                'state' => 'Hà Nội',
                'postcode' => '10000',
                'country' => 'Vietnam',
                'coordinates' => '21.0583,-105.8267',
                'phone_numbers' => '["+84-24-9876-5432"]',
                'emails' => '["<EMAIL>"]',
                'website' => 'https://skylounge.com',
                'social_links' => '{"facebook": "https://facebook.com/skylounge", "instagram": "https://instagram.com/skylounge", "twitter": "https://twitter.com/skylounge"}',
                'iframe' => '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.2!2d105.8267!3d21.0583!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1" width="600" height="450" style="border:0;" allowfullscreen loading="lazy"></iframe>',
                'categories' => 'Sky Bar, Lounge',
                'services' => 'Free WiFi, Meeting Room, 24/7 Reception'
            ]
        ];
    }
}
