<?php

namespace Bo<PERSON>ble\BookingPlace\Imports;

use Bo<PERSON>ble\BookingPlace\Enums\BookingPlaceStatusEnum;
use Bo<PERSON>ble\BookingPlace\Models\BookingCategory;
use Bo<PERSON>ble\BookingPlace\Models\BookingPlace;
use Bo<PERSON>ble\BookingPlace\Models\BookingService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class BookingPlaceImport implements
    ToCollection,
    WithHeadingRow,
    WithValidation,
    WithBatchInserts,
    WithChunkReading,
    SkipsOnError,
    SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    protected array $importErrors = [];
    protected int $successCount = 0;
    protected int $failureCount = 0;
    protected array $processedRows = [];

    /**
     * Expected column headers mapping
     */
    protected array $columnMapping = [
        'name' => 'name',
        'slug' => 'slug',
        'description' => 'description',
        'status' => 'status',
        'thumbnail' => 'thumbnail',
        'reservation_start_time' => 'reservation_start_time',
        'reservation_end_time' => 'reservation_end_time',
        'address' => 'address',
        'address_2' => 'address_2',
        'city' => 'city',
        'state' => 'state',
        'postcode' => 'postcode',
        'country' => 'country',
        'coordinates' => 'coordinates',
        'phone_numbers' => 'phone_numbers',
        'emails' => 'emails',
        'website' => 'website',
        'social_links' => 'social_links',
        'iframe' => 'iframe',
        'categories' => 'categories',
        'services' => 'services',
    ];

    /**
     * Validation rules for each row
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:220'],
            'slug' => ['nullable', 'string', 'max:220'],
            'description' => ['required', 'string'],
            'status' => ['nullable', Rule::in(BookingPlaceStatusEnum::values())],
            'thumbnail' => ['nullable', 'string', 'json'],
            'reservation_start_time' => ['required', 'date_format:H:i:s'],
            'reservation_end_time' => ['required', 'date_format:H:i:s'],
            'address' => ['required', 'string', 'max:220'],
            'address_2' => ['nullable', 'string', 'max:220'],
            'city' => ['required', 'string', 'max:220'],
            'state' => ['required', 'string', 'max:220'],
            'postcode' => ['required'],
            'country' => ['required', 'string', 'max:220'],
            'coordinates' => ['required', 'string', 'regex:/^-?\d{1,3}\.\d+,-?\d{1,3}\.\d+$/'],
            'phone_numbers' => ['nullable', 'string', 'json'],
            'emails' => ['nullable', 'string', 'json'],
            'website' => ['nullable', 'string', 'max:220', 'url'],
            'social_links' => ['nullable', 'string', 'json'],
            'iframe' => ['nullable', 'string'],
            'categories' => ['nullable', 'string'],
            'services' => ['nullable', 'string'],
        ];
    }

    /**
     * Custom validation messages
     */
    public function customValidationMessages(): array
    {
        return [
            'name.required' => 'The name field is required.',
            'name.max' => 'The name may not be greater than 220 characters.',
            'description.required' => 'The description field is required.',
            'reservation_start_time.required' => 'The reservation start time is required.',
            'reservation_start_time.date_format' => 'The reservation start time must be in format H:i:s (e.g., 11:00:00).',
            'reservation_end_time.required' => 'The reservation end time is required.',
            'reservation_end_time.date_format' => 'The reservation end time must be in format H:i:s (e.g., 23:00:00).',
            'address.required' => 'The address field is required.',
            'city.required' => 'The city field is required.',
            'state.required' => 'The state field is required.',
            'postcode.required' => 'The postcode field is required.',
            'country.required' => 'The country field is required.',
            'coordinates.required' => 'The coordinates field is required.',
            'coordinates.regex' => 'The coordinates must be in format latitude,longitude (e.g., 21.0285,-105.8542).',
            'thumbnail.json' => 'The thumbnail must be a valid JSON array of image URLs.',
            'phone_numbers.json' => 'The phone numbers must be a valid JSON array.',
            'emails.json' => 'The emails must be a valid JSON array.',
            'website.url' => 'The website must be a valid URL.',
            'social_links.json' => 'The social links must be a valid JSON object.',
            'status.in' => 'The status must be one of: ' . implode(', ', BookingPlaceStatusEnum::values()),
        ];
    }

    /**
     * Process each collection of rows
     */
    public function collection(Collection $rows): void
    {
        $rows->each(function ($row, $index) {
            try {
                $this->processRow($row, $index + 2); // +2 because Excel rows start at 1 and we have header
            } catch (\Exception $e) {
                $this->addError($index + 2, 'Processing error: ' . $e->getMessage());
                Log::error('BookingPlace import error on row ' . ($index + 2) . ': ' . $e->getMessage());
            }
        });
    }

    /**
     * Process individual row
     */
    protected function processRow($row, int $rowNumber): void
    {
        // Convert row to array and filter out empty values
        $data = $this->prepareRowData($row);

        // Validate the data
        $validator = Validator::make($data, $this->rules(), $this->customValidationMessages());

        if ($validator->fails()) {
            $this->addError($rowNumber, 'Validation failed: ' . implode(', ', $validator->errors()->all()));
            $this->failureCount++;
            return;
        }

        // Process the data within a transaction
        DB::transaction(function () use ($data, $rowNumber) {
            try {
                $bookingPlace = $this->createBookingPlace($data);
                $this->attachCategoriesAndServices($bookingPlace, $data);

                $this->successCount++;
                $this->processedRows[] = [
                    'row' => $rowNumber,
                    'name' => $data['name'],
                    'status' => 'success'
                ];
            } catch (\Exception $e) {
                $this->addError($rowNumber, 'Database error: ' . $e->getMessage());
                $this->failureCount++;
                throw $e; // Re-throw to rollback transaction
            }
        });
    }

    /**
     * Prepare row data for processing
     */
    protected function prepareRowData($row): array
    {
        $data = [];

        foreach ($this->columnMapping as $excelColumn => $dbColumn) {
            $value = $row[$excelColumn] ?? null;

            // Handle special cases
            switch ($dbColumn) {
                case 'slug':
                    $data[$dbColumn] = $value ? Str::slug($value) : Str::slug($row['name'] ?? '');
                    break;
                case 'status':
                    $data[$dbColumn] = $value ?: BookingPlaceStatusEnum::PUBLISHED;
                    break;
                case 'thumbnail':
                    $data[$dbColumn] = $value ? [$value] : [];
                    break;
                case 'phone_numbers':
                    $data[$dbColumn] = $value ? $this->parseJsonArray($value) : [];
                    break;
                case 'emails':
                    $data[$dbColumn] = $value ? $this->parseJsonArray($value) : [];
                    break;
                case 'social_links':
                    $data[$dbColumn] = $value ? $this->parseSocialLinks($value) : [];
                    break;
                default:
                    $data[$dbColumn] = $value;
                    break;
            }
        }

        return $data;
    }

    /**
     * Parse JSON array string
     */
    protected function parseJsonArray(string $value): array
    {
        // Try to decode as JSON first
        $decoded = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }

        // If not JSON, try to parse as comma-separated values
        return array_map('trim', explode(',', $value));
    }

    /**
     * Parse social links
     */
    protected function parseSocialLinks(string $value): array
    {
        $decoded = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }

        // If not JSON, assume it's a simple format like "facebook: url, twitter: url"
        $links = [];
        $pairs = explode(',', $value);
        foreach ($pairs as $pair) {
            $parts = explode(':', $pair, 2);
            if (count($parts) === 2) {
                $links[] = [
                    'platform' => trim($parts[0]),
                    'url' => trim($parts[1])
                ];
            }
        }

        return $links;
    }

    /**
     * Create BookingPlace record
     */
    protected function createBookingPlace(array $data): BookingPlace
    {
        // Remove relationship data before creating the place
        $placeData = collect($data)->except(['categories', 'services'])->toArray();

        return BookingPlace::create($placeData);
    }

    /**
     * Attach categories and services to the booking place
     */
    protected function attachCategoriesAndServices(BookingPlace $bookingPlace, array $data): void
    {
        // Attach categories
        if (!empty($data['categories'])) {
            $categoryIds = $this->resolveCategoryIds($data['categories']);
            if (!empty($categoryIds)) {
                $bookingPlace->categories()->attach($categoryIds);
            }
        }

        // Attach services
        if (!empty($data['services'])) {
            $serviceIds = $this->resolveServiceIds($data['services']);
            if (!empty($serviceIds)) {
                $bookingPlace->services()->attach($serviceIds);
            }
        }
    }

    /**
     * Resolve category names/IDs to IDs
     */
    protected function resolveCategoryIds(string $categories): array
    {
        $namesOrIds = array_map('trim', explode(',', $categories));
        $ids = [];

        foreach ($namesOrIds as $nameOrId) {
            // Try to find by ID first
            $category = BookingCategory::find($nameOrId);
            if (!$category) {
                // Try to find by name
                $category = BookingCategory::where('name', $nameOrId)->first();
            }

            if ($category) {
                $ids[] = $category->id;
            }
        }

        return array_unique($ids);
    }

    /**
     * Resolve service names/IDs to IDs
     */
    protected function resolveServiceIds(string $services): array
    {
        $namesOrIds = array_map('trim', explode(',', $services));
        $ids = [];

        foreach ($namesOrIds as $nameOrId) {
            // Try to find by ID first
            $service = BookingService::find($nameOrId);
            if (!$service) {
                // Try to find by name
                $service = BookingService::where('name', $nameOrId)->first();
            }

            if ($service) {
                $ids[] = $service->id;
            }
        }

        return array_unique($ids);
    }

    /**
     * Add error to the errors array
     */
    protected function addError(int $row, string $message): void
    {
        $this->importErrors[] = [
            'row' => $row,
            'message' => $message
        ];
    }

    /**
     * Get all errors
     */
    public function getErrors(): array
    {
        return $this->importErrors;
    }

    /**
     * Get success count
     */
    public function getSuccessCount(): int
    {
        return $this->successCount;
    }

    /**
     * Get failure count
     */
    public function getFailureCount(): int
    {
        return $this->failureCount;
    }

    /**
     * Get processed rows
     */
    public function getProcessedRows(): array
    {
        return $this->processedRows;
    }

    /**
     * Batch size for inserts
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * Chunk size for reading
     */
    public function chunkSize(): int
    {
        return 1000;
    }
}
