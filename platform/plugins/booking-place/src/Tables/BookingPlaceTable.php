<?php

namespace Bo<PERSON><PERSON>\BookingPlace\Tables;

use Bo<PERSON>ble\BookingPlace\Models\BookingPlace;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class BookingPlaceTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(BookingPlace::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('booking-place.create'))
            ->addActions([
                EditAction::make()->route('booking-place.edit'),
                DeleteAction::make()->route('booking-place.destroy'),
            ])
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('booking-place.edit'),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('booking-place.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                $query->select([
                    'id',
                    'name',
                    'created_at',
                    'status',
                ])->orderby('updated_at', 'desc');
            });
    }
}
